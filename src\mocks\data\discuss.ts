// Mock data for Discuss module
import type {
  User,
  Message,
  Channel,
  DirectMessage,
  Team,
  NotificationSettings,
  PresenceInfo,
  Call,
  CallParticipant,
  Bot,
  AutomationRule,
  ScheduledMessage,
  MessageArchive,
  RetentionPolicyRule,
  LegalHold,
  ExportRequest,
  ExternalIntegration,
  ERPIntegration,
  WebhookEndpoint,
  APIKey,
} from '../../modules/discuss/types';

// Mock users
export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'JD',
    status: 'online',
    lastSeen: new Date(),
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'J<PERSON>',
    status: 'away',
    lastSeen: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'MW',
    status: 'online',
    lastSeen: new Date(),
  },
  {
    id: '4',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'SJ',
    status: 'offline',
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
  },
  {
    id: '5',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'DB',
    status: 'busy',
    lastSeen: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
  },
];

// Mock channels
export const mockChannels: Channel[] = [
  {
    id: 'general',
    name: 'general',
    description: 'General discussion and announcements for the team',
    type: 'public',
    memberIds: ['1', '2', '3', '4', '5'],
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    lastActivity: new Date(),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'development',
    name: 'development',
    description: 'Development discussions and code reviews',
    type: 'public',
    memberIds: ['1', '3', '5'],
    createdBy: '1',
    createdAt: new Date('2024-01-02'),
    lastActivity: new Date(Date.now() - 30 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'design',
    name: 'design',
    description: 'Design discussions and feedback',
    type: 'public',
    memberIds: ['2', '4'],
    createdBy: '2',
    createdAt: new Date('2024-01-03'),
    lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
  {
    id: 'marketing',
    name: 'marketing',
    description: 'Marketing campaigns and strategies',
    type: 'private',
    memberIds: ['2', '4', '5'],
    createdBy: '2',
    createdAt: new Date('2024-01-04'),
    lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: true,
      allowFileUploads: true,
      allowExternalLinks: false,
    },
  },
  {
    id: 'random',
    name: 'random',
    description: 'Random conversations and fun stuff',
    type: 'public',
    memberIds: ['1', '2', '3', '4', '5'],
    createdBy: '3',
    createdAt: new Date('2024-01-05'),
    lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000),
    isArchived: false,
    settings: {
      notifications: false,
      allowFileUploads: true,
      allowExternalLinks: true,
    },
  },
];

// Mock messages with comprehensive data
export const mockMessages: Message[] = [
  // General channel messages
  {
    id: 'msg-1',
    content: 'Welcome everyone to our new discuss platform! 🎉 Feel free to share ideas, ask questions, and collaborate here.',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date('2024-01-10T09:30:00'),
    reactions: [
      { emoji: '👍', userIds: ['2', '3', '4'], count: 3 },
      { emoji: '🎉', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-2',
    content: 'This looks great! I love the clean interface. Can we also add file sharing capabilities?',
    authorId: '2',
    channelId: 'general',
    timestamp: new Date('2024-01-10T10:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['1'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-3',
    content: '@Jane Smith Yes! File sharing is definitely on the roadmap. We\'re also planning to add video calls and screen sharing.',
    authorId: '3',
    channelId: 'general',
    timestamp: new Date('2024-01-10T11:00:00'),
    reactions: [],
    attachments: [],
    mentions: ['2'],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-4',
    content: 'I\'ve uploaded the latest design mockups for review. Please take a look and let me know your thoughts!',
    authorId: '4',
    channelId: 'general',
    timestamp: new Date('2024-01-10T14:30:00'),
    reactions: [
      { emoji: '👀', userIds: ['1', '2', '3'], count: 3 },
      { emoji: '🔥', userIds: ['1'], count: 1 },
    ],
    attachments: [
      {
        id: 'att-1',
        name: 'design-mockups-v2.pdf',
        type: 'document',
        url: '/mock-files/design-mockups-v2.pdf',
        size: 2048576, // 2MB
        mimeType: 'application/pdf',
      },
      {
        id: 'att-2',
        name: 'homepage-screenshot.png',
        type: 'image',
        url: '/mock-files/homepage-screenshot.png',
        size: 512000, // 500KB
        mimeType: 'image/png',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-5',
    content: 'Great work @Sarah Johnson! The new color scheme looks much more professional. 💯',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date('2024-01-10T15:45:00'),
    reactions: [
      { emoji: '💯', userIds: ['2', '3', '4'], count: 3 },
    ],
    attachments: [],
    mentions: ['4'],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // Development channel messages
  {
    id: 'msg-6',
    content: 'Just pushed the new authentication system to the dev branch. Ready for testing!',
    authorId: '3',
    channelId: 'development',
    timestamp: new Date('2024-01-10T09:00:00'),
    reactions: [
      { emoji: '🚀', userIds: ['1', '5'], count: 2 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-7',
    content: 'Found a bug in the user registration flow. Creating a ticket now.',
    authorId: '5',
    channelId: 'development',
    timestamp: new Date('2024-01-10T11:30:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-8',
    content: 'Here\'s the error log from the staging server. Looks like a database connection issue.',
    authorId: '5',
    channelId: 'development',
    timestamp: new Date('2024-01-10T12:00:00'),
    reactions: [],
    attachments: [
      {
        id: 'att-3',
        name: 'error-log-2024-01-10.txt',
        type: 'document',
        url: '/mock-files/error-log-2024-01-10.txt',
        size: 15360, // 15KB
        mimeType: 'text/plain',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // Design channel messages
  {
    id: 'msg-9',
    content: 'Working on the new icon set for the mobile app. Here\'s the progress so far:',
    authorId: '2',
    channelId: 'design',
    timestamp: new Date('2024-01-09T16:20:00'),
    reactions: [
      { emoji: '🎨', userIds: ['4'], count: 1 },
    ],
    attachments: [
      {
        id: 'att-4',
        name: 'mobile-icons-preview.png',
        type: 'image',
        url: '/mock-files/mobile-icons-preview.png',
        size: 768000, // 750KB
        mimeType: 'image/png',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-10',
    content: 'Love the minimalist approach! Can we make the notification icon a bit more prominent?',
    authorId: '4',
    channelId: 'design',
    timestamp: new Date('2024-01-09T17:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // Direct messages (no channelId)
  {
    id: 'msg-11',
    content: 'Hey! I wanted to discuss the new project requirements with you. Do you have some time today?',
    authorId: '1',
    timestamp: new Date('2024-01-09T15:45:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-12',
    content: 'Sure! I\'m free after 5 PM. Should we schedule a quick call?',
    authorId: '2',
    timestamp: new Date('2024-01-09T16:12:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-13',
    content: 'Perfect! Let\'s do a video call at 5:30 PM. I\'ll send you the meeting link.',
    authorId: '1',
    timestamp: new Date('2024-01-09T16:15:00'),
    reactions: [
      { emoji: '👍', userIds: ['2'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-14',
    content: 'Good morning! I\'ve reviewed the requirements document. Looks great overall, just have a few questions about the timeline.',
    authorId: '1',
    timestamp: new Date('2024-01-10T09:00:00'),
    reactions: [],
    attachments: [
      {
        id: 'att-5',
        name: 'project-requirements-v3.docx',
        type: 'document',
        url: '/mock-files/project-requirements-v3.docx',
        size: 1024000, // 1MB
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      },
    ],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-15',
    content: 'Thanks for the review! I\'ll address those timeline questions in our call today.',
    authorId: '2',
    timestamp: new Date('2024-01-10T09:30:00'),
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },

  // More recent messages for testing
  {
    id: 'msg-16',
    content: 'Just deployed the hotfix to production. Everything looks good! 🎉',
    authorId: '5',
    channelId: 'development',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    reactions: [
      { emoji: '🎉', userIds: ['1', '3'], count: 2 },
      { emoji: '🚀', userIds: ['1'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-17',
    content: 'Team meeting in 30 minutes! Don\'t forget to join the video call.',
    authorId: '1',
    channelId: 'general',
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    reactions: [
      { emoji: '📅', userIds: ['2', '3', '4', '5'], count: 4 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'read',
  },
  {
    id: 'msg-18',
    content: 'Can someone help me with the CSS animation issue? It\'s not working in Safari.',
    authorId: '3',
    channelId: 'development',
    timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'delivered',
  },
  {
    id: 'msg-19',
    content: 'I can help! Safari has some quirks with CSS animations. Let me take a look.',
    authorId: '1',
    channelId: 'development',
    timestamp: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
    reactions: [
      { emoji: '🙏', userIds: ['3'], count: 1 },
    ],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'delivered',
  },
  {
    id: 'msg-20',
    content: 'Quick question about the API endpoint for user preferences. Is it /api/users/:id/preferences?',
    authorId: '2',
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    reactions: [],
    attachments: [],
    mentions: [],
    isDeleted: false,
    deliveryStatus: 'sent',
  },
];

// Mock direct messages
export const mockDirectMessages: DirectMessage[] = [
  {
    id: 'dm-1-2',
    participantIds: ['1', '2'],
    lastMessage: mockMessages.find(m => m.id === 'msg-5'),
    lastActivity: new Date('2024-01-09T16:12:00'),
    isArchived: false,
  },
  {
    id: 'dm-1-3',
    participantIds: ['1', '3'],
    lastActivity: new Date('2024-01-08T14:30:00'),
    isArchived: false,
  },
];

// Mock teams
export const mockTeams: Team[] = [
  {
    id: 'frontend-team',
    name: 'Frontend Team',
    description: 'UI/UX development and design implementation',
    memberIds: ['1', '2', '4'],
    channelIds: ['general', 'design'],
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    settings: {
      visibility: 'public',
      joinPolicy: 'open',
      allowMemberInvites: true,
    },
  },
  {
    id: 'backend-team',
    name: 'Backend Team',
    description: 'Server-side development and API management',
    memberIds: ['3', '5'],
    channelIds: ['development'],
    createdBy: '3',
    createdAt: new Date('2024-01-02'),
    settings: {
      visibility: 'public',
      joinPolicy: 'invite-only',
      allowMemberInvites: false,
    },
  },
];

// Mock notification settings
export const mockNotificationSettings: NotificationSettings = {
  desktop: true,
  sound: true,
  email: false,
  mobile: true,
  mentions: true,
  directMessages: true,
  channels: true,
  doNotDisturbStart: '22:00',
  doNotDisturbEnd: '08:00',
};

// Mock presence info
export const mockPresenceInfo: PresenceInfo[] = [
  {
    userId: '1',
    status: 'online',
    lastSeen: new Date(),
    isTyping: false,
  },
  {
    userId: '2',
    status: 'away',
    lastSeen: new Date(Date.now() - 15 * 60 * 1000),
    isTyping: true,
    currentChannel: 'general',
  },
  {
    userId: '3',
    status: 'online',
    lastSeen: new Date(),
    isTyping: false,
  },
  {
    userId: '4',
    status: 'offline',
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000),
    isTyping: false,
  },
  {
    userId: '5',
    status: 'busy',
    lastSeen: new Date(Date.now() - 30 * 60 * 1000),
    isTyping: false,
  },
];

// Helper functions to get mock data
export const getMockUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const getMockChannelById = (id: string): Channel | undefined => {
  return mockChannels.find(channel => channel.id === id);
};

export const getMockMessagesByChannelId = (channelId: string): Message[] => {
  return mockMessages.filter(message => message.channelId === channelId);
};

export const getMockDirectMessagesByUserId = (userId: string): DirectMessage[] => {
  return mockDirectMessages.filter(dm => dm.participantIds.includes(userId));
};

export const getMockTeamById = (id: string): Team | undefined => {
  return mockTeams.find(team => team.id === id);
};

// Mock calls
export const mockCalls: Call[] = [
  {
    id: 'call-1',
    type: 'video',
    channelId: 'general',
    participantIds: ['1', '2', '3'],
    startedBy: '1',
    startedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    endedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 15 * 60 * 1000), // 15 minutes duration
    status: 'ended',
    recordingUrl: '/recordings/call-1.mp4',
  },
  {
    id: 'call-2',
    type: 'voice',
    channelId: 'development',
    participantIds: ['2', '3', '4'],
    startedBy: '2',
    startedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    endedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 8 * 60 * 1000), // 8 minutes duration
    status: 'ended',
  },
  {
    id: 'call-3',
    type: 'video',
    participantIds: ['1', '4'], // Direct call
    startedBy: '4',
    startedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    endedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 25 * 60 * 1000), // 25 minutes duration
    status: 'ended',
    recordingUrl: '/recordings/call-3.mp4',
  },
  {
    id: 'call-4',
    type: 'voice',
    channelId: 'general',
    participantIds: ['1', '2', '3', '5'],
    startedBy: '3',
    startedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    endedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 45 * 60 * 1000), // 45 minutes duration
    status: 'ended',
  },
  {
    id: 'call-5',
    type: 'video',
    channelId: 'design',
    participantIds: ['2', '5'],
    startedBy: '5',
    startedAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago (ongoing)
    status: 'active',
  },
];

// Mock call participants
export const mockCallParticipants: { [callId: string]: CallParticipant[] } = {
  'call-1': [
    {
      userId: '1',
      joinedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      leftAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 15 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: false,
    },
    {
      userId: '2',
      joinedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30 * 1000),
      leftAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 15 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: true,
    },
    {
      userId: '3',
      joinedAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 2 * 60 * 1000),
      leftAt: new Date(Date.now() - 2 * 60 * 60 * 1000 + 12 * 60 * 1000),
      isMuted: true,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
  ],
  'call-2': [
    {
      userId: '2',
      joinedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      leftAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 8 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
    {
      userId: '3',
      joinedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 15 * 1000),
      leftAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 8 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
    {
      userId: '4',
      joinedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 45 * 1000),
      leftAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 6 * 60 * 1000),
      isMuted: true,
      isVideoEnabled: false,
      isScreenSharing: false,
    },
  ],
  'call-5': [
    {
      userId: '2',
      joinedAt: new Date(Date.now() - 5 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: false,
    },
    {
      userId: '5',
      joinedAt: new Date(Date.now() - 5 * 60 * 1000),
      isMuted: false,
      isVideoEnabled: true,
      isScreenSharing: true,
    },
  ],
};

// Helper functions for calls
export const getMockCallById = (id: string): Call | undefined => {
  return mockCalls.find(call => call.id === id);
};

export const getMockCallsByChannelId = (channelId: string): Call[] => {
  return mockCalls.filter(call => call.channelId === channelId);
};

export const getMockCallsByUserId = (userId: string): Call[] => {
  return mockCalls.filter(call => call.participantIds.includes(userId));
};

export const getMockCallParticipants = (callId: string): CallParticipant[] => {
  return mockCallParticipants[callId] || [];
};

// Mock bots
export const mockBots: Bot[] = [
  {
    id: 'bot-faq',
    name: 'FAQ Assistant',
    description: 'Answers frequently asked questions automatically',
    avatar: '🤖',
    type: 'faq',
    commands: [
      {
        command: '/faq',
        description: 'Search FAQ database',
        usage: '/faq <question>',
        permissions: ['read'],
        handler: 'handleFAQQuery',
        parameters: [
          {
            name: 'question',
            type: 'string',
            required: true,
            description: 'The question to search for',
          },
        ],
      },
      {
        command: '/help',
        description: 'Show available commands',
        usage: '/help',
        permissions: ['read'],
        handler: 'handleHelp',
      },
    ],
    triggers: [
      {
        id: 'faq-keyword',
        type: 'keyword',
        pattern: 'how to,what is,where can,help me',
        conditions: [],
        action: {
          type: 'reply',
          config: {
            template: 'I can help you with that! Try using /faq <your question>',
          },
        },
        enabled: true,
      },
    ],
    config: {
      permissions: {
        channels: ['general', 'support'],
        users: [],
        commands: ['/faq', '/help'],
        canReadHistory: true,
        canSendDM: false,
        canMentionUsers: false,
      },
      rateLimiting: {
        maxRequestsPerMinute: 10,
        maxRequestsPerHour: 100,
        cooldownPeriod: 5,
      },
      context: {
        rememberConversations: true,
        contextWindow: 5,
        persistUserData: false,
      },
    },
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-15'),
    lastActive: new Date(),
    analytics: {
      totalInteractions: 156,
      successfulResponses: 142,
      failedResponses: 14,
      averageResponseTime: 250,
      lastInteraction: new Date(),
      popularCommands: {
        '/faq': 89,
        '/help': 67,
      },
    },
  },
  {
    id: 'bot-ai-assistant',
    name: 'AI Assistant',
    description: 'Intelligent assistant powered by AI for complex queries',
    avatar: '🧠',
    type: 'ai_assistant',
    commands: [
      {
        command: '/ask',
        description: 'Ask the AI assistant anything',
        usage: '/ask <question>',
        permissions: ['read', 'write'],
        handler: 'handleAIQuery',
        parameters: [
          {
            name: 'question',
            type: 'string',
            required: true,
            description: 'Your question for the AI',
          },
        ],
      },
      {
        command: '/summarize',
        description: 'Summarize recent conversation',
        usage: '/summarize [count]',
        permissions: ['read'],
        handler: 'handleSummarize',
        parameters: [
          {
            name: 'count',
            type: 'number',
            required: false,
            description: 'Number of messages to summarize',
            defaultValue: 10,
          },
        ],
      },
    ],
    triggers: [
      {
        id: 'ai-mention',
        type: 'mention',
        pattern: '@ai-assistant',
        conditions: [],
        action: {
          type: 'reply',
          config: {
            useAI: true,
            context: 'conversation',
          },
        },
        enabled: true,
      },
    ],
    config: {
      permissions: {
        channels: ['general', 'development', 'support'],
        users: [],
        commands: ['/ask', '/summarize'],
        canReadHistory: true,
        canSendDM: true,
        canMentionUsers: true,
      },
      rateLimiting: {
        maxRequestsPerMinute: 5,
        maxRequestsPerHour: 50,
        cooldownPeriod: 10,
      },
      context: {
        rememberConversations: true,
        contextWindow: 20,
        persistUserData: true,
      },
      nlp: {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 500,
        systemPrompt: 'You are a helpful assistant for a team collaboration platform.',
      },
    },
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-20'),
    lastActive: new Date(),
    analytics: {
      totalInteractions: 89,
      successfulResponses: 85,
      failedResponses: 4,
      averageResponseTime: 1200,
      lastInteraction: new Date(),
      popularCommands: {
        '/ask': 52,
        '/summarize': 37,
      },
    },
  },
];

// Mock automation rules
export const mockAutomationRules: AutomationRule[] = [
  {
    id: 'auto-welcome',
    name: 'Welcome New Members',
    description: 'Automatically welcome new team members when they join a channel',
    trigger: {
      type: 'user_joined',
      config: {
        channels: ['general'],
      },
    },
    conditions: [],
    actions: [
      {
        type: 'send_message',
        config: {
          channelId: 'general',
          content: 'Welcome to the team, {{user.name}}! 👋 Feel free to introduce yourself and ask any questions.',
        },
        delay: 2000,
      },
    ],
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-10'),
    lastExecuted: new Date(),
    executionCount: 23,
  },
  {
    id: 'auto-urgent-notify',
    name: 'Urgent Message Notification',
    description: 'Send email notifications for messages marked as urgent',
    trigger: {
      type: 'message_sent',
      config: {
        keywords: ['urgent', 'emergency', 'critical'],
      },
    },
    conditions: [
      {
        field: 'message.content',
        operator: 'contains',
        value: 'urgent',
      },
    ],
    actions: [
      {
        type: 'email',
        config: {
          recipients: ['<EMAIL>'],
          subject: 'Urgent Message Alert',
          template: 'urgent_message',
        },
      },
    ],
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-12'),
    lastExecuted: new Date(Date.now() - 2 * 60 * 60 * 1000),
    executionCount: 7,
  },
];

// Mock scheduled messages
export const mockScheduledMessages: ScheduledMessage[] = [
  {
    id: 'sched-1',
    content: 'Daily standup reminder: Please share your updates in the #development channel! 📝',
    channelId: 'development',
    authorId: '1',
    scheduledFor: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    status: 'pending',
    recurring: {
      type: 'daily',
      interval: 1,
      daysOfWeek: [1, 2, 3, 4, 5], // Weekdays only
    },
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'sched-2',
    content: 'Weekly team meeting in 30 minutes! Join the video call in #general 📹',
    channelId: 'general',
    authorId: '1',
    scheduledFor: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
    status: 'pending',
    recurring: {
      type: 'weekly',
      interval: 1,
      daysOfWeek: [1], // Mondays
    },
    createdAt: new Date('2024-01-20'),
  },
];

// Mock message archives
export const mockMessageArchives: MessageArchive[] = [
  {
    id: 'archive-1',
    messageId: 'msg-old-1',
    channelId: 'general',
    authorId: '2',
    content: 'This is an archived message from last year',
    timestamp: new Date('2023-06-15T10:30:00'),
    archivedAt: new Date('2024-01-01'),
    retentionPolicy: 'policy-1',
    metadata: {
      originalFormat: 'json',
      encryption: true,
      checksum: 'sha256:abc123...',
      tags: ['archived', 'general'],
      legalHold: false,
      exportable: true,
    },
  },
  {
    id: 'archive-2',
    messageId: 'msg-old-2',
    channelId: 'development',
    authorId: '3',
    content: 'Code review completed for PR #123',
    timestamp: new Date('2023-08-20T14:15:00'),
    archivedAt: new Date('2024-01-01'),
    retentionPolicy: 'policy-2',
    metadata: {
      originalFormat: 'json',
      encryption: true,
      checksum: 'sha256:def456...',
      tags: ['archived', 'development', 'code-review'],
      legalHold: true,
      exportable: true,
    },
  },
];

// Mock retention policies
export const mockRetentionPolicies: RetentionPolicyRule[] = [
  {
    id: 'policy-1',
    name: 'General Channel Retention',
    description: 'Archive messages in general channels after 1 year',
    scope: {
      channels: ['general'],
      users: [],
      messageTypes: ['text', 'file'],
    },
    duration: 365,
    action: 'archive',
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    lastApplied: new Date('2024-01-01'),
  },
  {
    id: 'policy-2',
    name: 'Development Channel Retention',
    description: 'Archive development messages after 2 years',
    scope: {
      channels: ['development'],
      users: [],
      messageTypes: ['text', 'file', 'image'],
    },
    duration: 730,
    action: 'archive',
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-01'),
    lastApplied: new Date('2024-01-01'),
  },
];

// Mock legal holds
export const mockLegalHolds: LegalHold[] = [
  {
    id: 'hold-1',
    name: 'Project Alpha Investigation',
    description: 'Legal hold for Project Alpha related communications',
    scope: {
      users: ['2', '3'],
      channels: ['development'],
      keywords: ['project alpha', 'alpha project'],
      dateRange: {
        from: new Date('2023-06-01'),
        to: new Date('2023-12-31'),
      },
    },
    status: 'active',
    createdBy: '1',
    createdAt: new Date('2024-01-05'),
    reason: 'Litigation hold for ongoing legal proceedings',
  },
];

// Mock export requests
export const mockExportRequests: ExportRequest[] = [
  {
    id: 'export-1',
    name: 'Q4 2023 Communications Export',
    format: 'json',
    scope: {
      channels: ['general', 'development'],
      users: [],
      dateRange: {
        from: new Date('2023-10-01'),
        to: new Date('2023-12-31'),
      },
      includeAttachments: true,
      includeDeleted: false,
    },
    status: 'completed',
    requestedBy: '1',
    requestedAt: new Date('2024-01-10'),
    completedAt: new Date('2024-01-10T10:30:00'),
    downloadUrl: '/api/exports/export-1/download',
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    options: {
      anonymizeUsers: false,
      includeMetadata: true,
      compression: true,
    },
  },
  {
    id: 'export-2',
    name: 'Legal Hold Export',
    format: 'pdf',
    scope: {
      channels: ['development'],
      users: ['2', '3'],
      dateRange: {
        from: new Date('2023-06-01'),
        to: new Date('2023-12-31'),
      },
      includeAttachments: false,
      includeDeleted: true,
    },
    status: 'processing',
    requestedBy: '1',
    requestedAt: new Date(),
    options: {
      anonymizeUsers: false,
      includeMetadata: true,
      compression: false,
      watermark: 'CONFIDENTIAL - LEGAL HOLD',
    },
  },
];

// Mock external integrations
export const mockExternalIntegrations: ExternalIntegration[] = [
  {
    id: 'int-slack',
    name: 'Slack Integration',
    type: 'slack',
    status: 'active',
    config: {
      syncDirection: 'bidirectional',
      syncFrequency: 5,
      autoSync: true,
      retryAttempts: 3,
      timeout: 30,
      batchSize: 100,
      filters: [
        {
          field: 'channel',
          operator: 'equals',
          value: 'general',
          exclude: false,
        },
      ],
    },
    credentials: {
      type: 'oauth',
      data: {}, // Encrypted
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    },
    mapping: {
      userMapping: {
        'slack-user-1': '1',
        'slack-user-2': '2',
      },
      channelMapping: {
        'slack-general': 'general',
      },
      messageMapping: {},
      customFields: {},
    },
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-15'),
    lastSync: new Date(),
    syncStats: {
      totalSynced: 1250,
      successfulSyncs: 1230,
      failedSyncs: 20,
      averageSyncTime: 2500,
    },
  },
];

// Mock ERP integrations
export const mockERPIntegrations: ERPIntegration[] = [
  {
    id: 'erp-crm-1',
    module: 'CRM',
    recordType: 'lead',
    recordId: 'lead-123',
    channelId: 'sales',
    linkType: 'reference',
    config: {
      autoNotify: true,
      syncFields: ['status', 'value', 'owner'],
      notificationEvents: ['status_change', 'value_update'],
      accessPermissions: ['read', 'write'],
    },
    createdAt: new Date('2024-01-20'),
    lastUpdated: new Date(),
  },
];

// Mock webhooks
export const mockWebhooks: WebhookEndpoint[] = [
  {
    id: 'webhook-1',
    name: 'External System Notifications',
    url: 'https://external-system.com/webhooks/discuss',
    events: [
      { type: 'message_created' },
      { type: 'channel_created' },
    ],
    headers: {
      'Authorization': 'Bearer token123',
      'Content-Type': 'application/json',
    },
    secret: 'webhook-secret-123',
    enabled: true,
    retryPolicy: {
      maxRetries: 3,
      backoffMultiplier: 2,
      maxBackoffSeconds: 300,
    },
    createdBy: '1',
    createdAt: new Date('2024-01-18'),
    lastTriggered: new Date(),
    stats: {
      totalDeliveries: 45,
      successfulDeliveries: 42,
      failedDeliveries: 3,
      averageResponseTime: 150,
    },
  },
];

// Mock API keys
export const mockAPIKeys: APIKey[] = [
  {
    id: 'api-key-1',
    name: 'Mobile App Integration',
    key: 'hashed-key-value', // This would be hashed
    permissions: [
      {
        resource: 'messages',
        actions: ['read', 'create'],
        scope: { channels: ['general'] },
      },
      {
        resource: 'channels',
        actions: ['read'],
      },
    ],
    rateLimit: {
      maxRequestsPerMinute: 100,
      maxRequestsPerHour: 1000,
      cooldownPeriod: 60,
    },
    enabled: true,
    createdBy: '1',
    createdAt: new Date('2024-01-22'),
    lastUsed: new Date(),
    usage: {
      totalRequests: 2340,
      requestsToday: 156,
      requestsThisMonth: 4567,
      averageResponseTime: 85,
      errorRate: 0.02,
      lastRequest: new Date(),
    },
  },
];

// Helper functions for bots
export const getMockBotById = (id: string): Bot | undefined => {
  return mockBots.find(bot => bot.id === id);
};

export const getMockBotsByType = (type: Bot['type']): Bot[] => {
  return mockBots.filter(bot => bot.type === type);
};

export const getMockEnabledBots = (): Bot[] => {
  return mockBots.filter(bot => bot.enabled);
};

// Helper functions for automation
export const getMockAutomationRuleById = (id: string): AutomationRule | undefined => {
  return mockAutomationRules.find(rule => rule.id === id);
};

export const getMockEnabledAutomationRules = (): AutomationRule[] => {
  return mockAutomationRules.filter(rule => rule.enabled);
};

// Helper functions for scheduled messages
export const getMockScheduledMessageById = (id: string): ScheduledMessage | undefined => {
  return mockScheduledMessages.find(msg => msg.id === id);
};

export const getMockPendingScheduledMessages = (): ScheduledMessage[] => {
  return mockScheduledMessages.filter(msg => msg.status === 'pending');
};

export const getMockScheduledMessagesByChannel = (channelId: string): ScheduledMessage[] => {
  return mockScheduledMessages.filter(msg => msg.channelId === channelId);
};

// Helper functions for archives
export const getMockArchivedMessageById = (id: string): MessageArchive | undefined => {
  return mockMessageArchives.find(archive => archive.id === id);
};

export const getMockArchivedMessagesByChannel = (channelId: string): MessageArchive[] => {
  return mockMessageArchives.filter(archive => archive.channelId === channelId);
};

export const getMockArchivedMessagesByUser = (userId: string): MessageArchive[] => {
  return mockMessageArchives.filter(archive => archive.authorId === userId);
};

// Helper functions for retention policies
export const getMockRetentionPolicyById = (id: string): RetentionPolicyRule | undefined => {
  return mockRetentionPolicies.find(policy => policy.id === id);
};

export const getMockEnabledRetentionPolicies = (): RetentionPolicyRule[] => {
  return mockRetentionPolicies.filter(policy => policy.enabled);
};

// Helper functions for legal holds
export const getMockLegalHoldById = (id: string): LegalHold | undefined => {
  return mockLegalHolds.find(hold => hold.id === id);
};

export const getMockActiveLegalHolds = (): LegalHold[] => {
  return mockLegalHolds.filter(hold => hold.status === 'active');
};

// Helper functions for exports
export const getMockExportRequestById = (id: string): ExportRequest | undefined => {
  return mockExportRequests.find(request => request.id === id);
};

export const getMockExportRequestsByUser = (userId: string): ExportRequest[] => {
  return mockExportRequests.filter(request => request.requestedBy === userId);
};

export const getMockExportRequestsByStatus = (status: ExportRequest['status']): ExportRequest[] => {
  return mockExportRequests.filter(request => request.status === status);
};

// Helper functions for integrations
export const getMockIntegrationById = (id: string): ExternalIntegration | undefined => {
  return mockExternalIntegrations.find(integration => integration.id === id);
};

export const getMockIntegrationsByType = (type: ExternalIntegration['type']): ExternalIntegration[] => {
  return mockExternalIntegrations.filter(integration => integration.type === type);
};

export const getMockEnabledIntegrations = (): ExternalIntegration[] => {
  return mockExternalIntegrations.filter(integration => integration.enabled);
};

// Helper functions for ERP integrations
export const getMockERPIntegrationById = (id: string): ERPIntegration | undefined => {
  return mockERPIntegrations.find(integration => integration.id === id);
};

export const getMockERPIntegrationsByModule = (module: string): ERPIntegration[] => {
  return mockERPIntegrations.filter(integration => integration.module === module);
};

export const getMockERPIntegrationsByChannel = (channelId: string): ERPIntegration[] => {
  return mockERPIntegrations.filter(integration => integration.channelId === channelId);
};

// Helper functions for webhooks
export const getMockWebhookById = (id: string): WebhookEndpoint | undefined => {
  return mockWebhooks.find(webhook => webhook.id === id);
};

export const getMockEnabledWebhooks = (): WebhookEndpoint[] => {
  return mockWebhooks.filter(webhook => webhook.enabled);
};

// Helper functions for API keys
export const getMockAPIKeyById = (id: string): APIKey | undefined => {
  return mockAPIKeys.find(key => key.id === id);
};

export const getMockEnabledAPIKeys = (): APIKey[] => {
  return mockAPIKeys.filter(key => key.enabled);
};

export const getMockAPIKeysByUser = (userId: string): APIKey[] => {
  return mockAPIKeys.filter(key => key.createdBy === userId);
};
