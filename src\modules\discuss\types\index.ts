// Discuss module type definitions

// Core types
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen?: Date;
}

export interface Message {
  id: string;
  content: string;
  authorId: string;
  channelId?: string;
  threadId?: string;
  parentMessageId?: string;
  timestamp: Date;
  editedAt?: Date;
  reactions: Reaction[];
  attachments: Attachment[];
  mentions: string[];
  isDeleted: boolean;
  deliveryStatus: 'sent' | 'delivered' | 'read' | 'failed';
}

export interface Channel {
  id: string;
  name: string;
  description?: string;
  type: 'public' | 'private' | 'direct';
  memberIds: string[];
  createdBy: string;
  createdAt: Date;
  lastActivity?: Date;
  isArchived: boolean;
  settings: ChannelSettings;
}

export interface DirectMessage {
  id: string;
  participantIds: string[];
  lastMessage?: Message;
  lastActivity: Date;
  isArchived: boolean;
}

export interface Team {
  id: string;
  name: string;
  description?: string;
  memberIds: string[];
  channelIds: string[];
  createdBy: string;
  createdAt: Date;
  settings: TeamSettings;
}

export interface Reaction {
  emoji: string;
  userIds: string[];
  count: number;
}

export interface Attachment {
  id: string;
  name: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'other';
  url: string;
  size: number;
  mimeType: string;
}

export interface ChannelSettings {
  notifications: boolean;
  muteUntil?: Date;
  allowFileUploads: boolean;
  allowExternalLinks: boolean;
  retentionPolicy?: RetentionPolicy;
}

export interface TeamSettings {
  visibility: 'public' | 'private';
  joinPolicy: 'open' | 'invite-only' | 'admin-approval';
  allowMemberInvites: boolean;
}

export interface RetentionPolicy {
  enabled: boolean;
  duration: number; // in days
  autoDelete: boolean;
}

export interface NotificationSettings {
  desktop: boolean;
  sound: boolean;
  email: boolean;
  mobile: boolean;
  mentions: boolean;
  directMessages: boolean;
  channels: boolean;
  doNotDisturbStart?: string;
  doNotDisturbEnd?: string;
}

export interface PresenceInfo {
  userId: string;
  status: User['status'];
  lastSeen: Date;
  isTyping: boolean;
  currentChannel?: string;
}

// Event types for real-time updates
export interface MessageEvent {
  type: 'message_created' | 'message_updated' | 'message_deleted';
  message: Message;
  channelId: string;
}

export interface PresenceEvent {
  type: 'user_online' | 'user_offline' | 'user_typing' | 'user_stopped_typing';
  userId: string;
  channelId?: string;
  presence: PresenceInfo;
}

export interface ChannelEvent {
  type: 'channel_created' | 'channel_updated' | 'channel_deleted' | 'user_joined' | 'user_left';
  channel: Channel;
  userId?: string;
}

// API response types
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Search types
export interface SearchQuery {
  query: string;
  channelId?: string;
  userId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  messageType?: 'text' | 'file' | 'image' | 'video';
}

export interface SearchResult {
  message: Message;
  channel: Channel;
  author: User;
  highlights: string[];
}

// Integration types
export interface Integration {
  id: string;
  name: string;
  type: 'webhook' | 'bot' | 'external_app';
  enabled: boolean;
  config: Record<string, any>;
}

export interface Webhook {
  id: string;
  url: string;
  events: string[];
  secret?: string;
  enabled: boolean;
}

// Bot types
export interface Bot {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  type: 'faq' | 'ai_assistant' | 'workflow' | 'custom';
  commands: BotCommand[];
  triggers: BotTrigger[];
  config: BotConfig;
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastActive?: Date;
  analytics: BotAnalytics;
}

export interface BotCommand {
  command: string;
  description: string;
  usage: string;
  permissions: string[];
  handler: string; // Function name or endpoint
  parameters?: BotParameter[];
}

export interface BotTrigger {
  id: string;
  type: 'keyword' | 'mention' | 'schedule' | 'event' | 'regex';
  pattern: string;
  conditions?: TriggerCondition[];
  action: BotAction;
  enabled: boolean;
}

export interface BotParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'user' | 'channel';
  required: boolean;
  description: string;
  defaultValue?: any;
}

export interface TriggerCondition {
  field: string;
  operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'regex';
  value: any;
}

export interface BotAction {
  type: 'reply' | 'dm' | 'webhook' | 'workflow' | 'function';
  config: Record<string, any>;
  delay?: number; // in milliseconds
}

export interface BotConfig {
  permissions: BotPermissions;
  rateLimiting: RateLimitConfig;
  context: ContextConfig;
  nlp?: NLPConfig;
}

export interface BotPermissions {
  channels: string[]; // Channel IDs where bot can operate
  users: string[]; // User IDs who can interact with bot
  commands: string[]; // Commands the bot can execute
  canReadHistory: boolean;
  canSendDM: boolean;
  canMentionUsers: boolean;
}

export interface RateLimitConfig {
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  cooldownPeriod: number; // in seconds
}

export interface ContextConfig {
  rememberConversations: boolean;
  contextWindow: number; // Number of previous messages to consider
  persistUserData: boolean;
}

export interface NLPConfig {
  provider: 'openai' | 'anthropic' | 'local' | 'custom';
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
}

export interface BotAnalytics {
  totalInteractions: number;
  successfulResponses: number;
  failedResponses: number;
  averageResponseTime: number;
  lastInteraction?: Date;
  popularCommands: Record<string, number>;
}

// Automation types
export interface AutomationRule {
  id: string;
  name: string;
  description?: string;
  trigger: AutomationTrigger;
  conditions: AutomationCondition[];
  actions: AutomationAction[];
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastExecuted?: Date;
  executionCount: number;
}

export interface AutomationTrigger {
  type: 'message_sent' | 'user_joined' | 'user_left' | 'schedule' | 'webhook';
  config: Record<string, any>;
}

export interface AutomationCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
  value: any;
}

export interface AutomationAction {
  type: 'send_message' | 'create_channel' | 'add_user' | 'remove_user' | 'webhook' | 'email';
  config: Record<string, any>;
  delay?: number;
}

// Scheduled message types
export interface ScheduledMessage {
  id: string;
  content: string;
  channelId: string;
  authorId: string;
  scheduledFor: Date;
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  recurring?: RecurrenceConfig;
  createdAt: Date;
  sentAt?: Date;
  error?: string;
}

export interface RecurrenceConfig {
  type: 'daily' | 'weekly' | 'monthly' | 'custom';
  interval: number;
  daysOfWeek?: number[]; // 0-6, Sunday = 0
  endDate?: Date;
  maxOccurrences?: number;
}

// Archival & Compliance types
export interface MessageArchive {
  id: string;
  messageId: string;
  channelId: string;
  authorId: string;
  content: string;
  timestamp: Date;
  archivedAt: Date;
  retentionPolicy: string;
  metadata: ArchiveMetadata;
}

export interface ArchiveMetadata {
  originalFormat: string;
  encryption: boolean;
  checksum: string;
  tags: string[];
  legalHold: boolean;
  exportable: boolean;
}

export interface RetentionPolicyRule {
  id: string;
  name: string;
  description?: string;
  scope: RetentionScope;
  duration: number; // in days
  action: 'archive' | 'delete' | 'anonymize';
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastApplied?: Date;
}

export interface RetentionScope {
  channels: string[];
  users: string[];
  messageTypes: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
}

export interface LegalHold {
  id: string;
  name: string;
  description?: string;
  scope: LegalHoldScope;
  status: 'active' | 'released' | 'expired';
  createdBy: string;
  createdAt: Date;
  expiresAt?: Date;
  releasedAt?: Date;
  reason: string;
}

export interface LegalHoldScope {
  users: string[];
  channels: string[];
  keywords: string[];
  dateRange: {
    from: Date;
    to?: Date;
  };
}

export interface ExportRequest {
  id: string;
  name: string;
  format: 'json' | 'csv' | 'pdf' | 'html';
  scope: ExportScope;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  requestedBy: string;
  requestedAt: Date;
  completedAt?: Date;
  downloadUrl?: string;
  expiresAt?: Date;
  error?: string;
  options: ExportOptions;
}

export interface ExportScope {
  channels: string[];
  users: string[];
  dateRange: {
    from: Date;
    to: Date;
  };
  includeAttachments: boolean;
  includeDeleted: boolean;
}

export interface ExportOptions {
  anonymizeUsers: boolean;
  includeMetadata: boolean;
  compression: boolean;
  password?: string;
  watermark?: string;
}

export interface ComplianceReport {
  id: string;
  type: 'retention' | 'legal_hold' | 'export' | 'audit';
  period: {
    from: Date;
    to: Date;
  };
  status: 'generating' | 'completed' | 'failed';
  generatedBy: string;
  generatedAt: Date;
  data: ComplianceData;
  downloadUrl?: string;
}

export interface ComplianceData {
  totalMessages: number;
  archivedMessages: number;
  deletedMessages: number;
  legalHoldMessages: number;
  exportRequests: number;
  violations: ComplianceViolation[];
}

export interface ComplianceViolation {
  id: string;
  type: 'retention_exceeded' | 'unauthorized_access' | 'export_failure';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detectedAt: Date;
  resolvedAt?: Date;
  affectedItems: string[];
}

// External Integration types
export interface ExternalIntegration {
  id: string;
  name: string;
  type: 'slack' | 'teams' | 'whatsapp' | 'email' | 'webhook' | 'api' | 'erp';
  status: 'active' | 'inactive' | 'error' | 'pending';
  config: IntegrationConfig;
  credentials: IntegrationCredentials;
  mapping: IntegrationMapping;
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastSync?: Date;
  syncStats: SyncStats;
}

export interface IntegrationConfig {
  syncDirection: 'inbound' | 'outbound' | 'bidirectional';
  syncFrequency: number; // in minutes
  autoSync: boolean;
  retryAttempts: number;
  timeout: number; // in seconds
  batchSize: number;
  filters: IntegrationFilter[];
}

export interface IntegrationCredentials {
  type: 'oauth' | 'api_key' | 'basic_auth' | 'certificate';
  data: Record<string, string>; // Encrypted storage
  expiresAt?: Date;
  refreshToken?: string;
}

export interface IntegrationMapping {
  userMapping: Record<string, string>; // External ID -> Internal ID
  channelMapping: Record<string, string>;
  messageMapping: Record<string, string>;
  customFields: Record<string, string>;
}

export interface IntegrationFilter {
  field: string;
  operator: 'equals' | 'contains' | 'starts_with' | 'regex';
  value: string;
  exclude: boolean;
}

export interface SyncStats {
  totalSynced: number;
  successfulSyncs: number;
  failedSyncs: number;
  lastError?: string;
  averageSyncTime: number;
}

// ERP Integration types
export interface ERPIntegration {
  id: string;
  module: string; // CRM, Sales, Inventory, etc.
  recordType: string;
  recordId: string;
  channelId: string;
  linkType: 'reference' | 'notification' | 'sync';
  config: ERPLinkConfig;
  createdAt: Date;
  lastUpdated?: Date;
}

export interface ERPLinkConfig {
  autoNotify: boolean;
  syncFields: string[];
  notificationEvents: string[];
  accessPermissions: string[];
}

// Webhook types
export interface WebhookEndpoint {
  id: string;
  name: string;
  url: string;
  events: WebhookEvent[];
  headers: Record<string, string>;
  secret?: string;
  enabled: boolean;
  retryPolicy: WebhookRetryPolicy;
  createdBy: string;
  createdAt: Date;
  lastTriggered?: Date;
  stats: WebhookStats;
}

export interface WebhookEvent {
  type: string;
  filters?: Record<string, any>;
}

export interface WebhookRetryPolicy {
  maxRetries: number;
  backoffMultiplier: number;
  maxBackoffSeconds: number;
}

export interface WebhookStats {
  totalDeliveries: number;
  successfulDeliveries: number;
  failedDeliveries: number;
  averageResponseTime: number;
  lastError?: string;
}

export interface WebhookDelivery {
  id: string;
  webhookId: string;
  event: string;
  payload: Record<string, any>;
  status: 'pending' | 'delivered' | 'failed' | 'retrying';
  attempts: number;
  createdAt: Date;
  deliveredAt?: Date;
  responseCode?: number;
  responseBody?: string;
  error?: string;
}

// API types for developers
export interface APIKey {
  id: string;
  name: string;
  key: string; // Hashed
  permissions: APIPermission[];
  rateLimit: RateLimitConfig;
  enabled: boolean;
  createdBy: string;
  createdAt: Date;
  lastUsed?: Date;
  expiresAt?: Date;
  usage: APIUsageStats;
}

export interface APIPermission {
  resource: string;
  actions: string[];
  scope?: Record<string, any>;
}

export interface APIUsageStats {
  totalRequests: number;
  requestsToday: number;
  requestsThisMonth: number;
  averageResponseTime: number;
  errorRate: number;
  lastRequest?: Date;
}

// File upload types
export interface FileUploadProgress {
  fileId: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'failed';
  error?: string;
}

// Voice/Video call types
export interface Call {
  id: string;
  type: 'voice' | 'video';
  channelId?: string;
  participantIds: string[];
  startedBy: string;
  startedAt: Date;
  endedAt?: Date;
  status: 'ringing' | 'active' | 'ended';
  recordingUrl?: string;
}

export interface CallParticipant {
  userId: string;
  joinedAt: Date;
  leftAt?: Date;
  isMuted: boolean;
  isVideoEnabled: boolean;
  isScreenSharing: boolean;
}

// Export all types
export type * from './index';
