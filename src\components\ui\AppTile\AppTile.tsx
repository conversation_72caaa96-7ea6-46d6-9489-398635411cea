import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface AppTileProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  'data-testid'?: string;
  color?: string; // Primary color for the app
  gradient?: boolean; // Whether to use gradient background for icon
  isActive?: boolean; // Whether the app is active/available
  isPremium?: boolean; // Whether this is a premium app
  viewMode?: 'grid' | 'kanban'; // View mode for different layouts
}

const AppTile: React.FC<AppTileProps> = ({
  title,
  description,
  icon,
  onClick,
  disabled = false,
  className = '',
  'data-testid': testId,
  color = '#f97316', // Default orange color
  gradient = true,
  isActive = true,
  isPremium = false,
}) => {
  const { colors, isDark } = useThemeStore();

  const handleClick = () => {
    if (!disabled && onClick) {
      onClick();
    }
  };

  const isInactive = !isActive;
  const baseClasses = cn(
    'relative group cursor-pointer',
    'rounded-xl p-4 sm:p-6 transition-all duration-300 ease-out',
    'border border-transparent',
    'focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2',
    'min-h-[120px] sm:min-h-[140px] flex flex-col justify-center',
    disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
    isInactive && 'opacity-60',
    className
  );

  // Create dark card design similar to the attached image
  const tileStyles = {
    backgroundColor: isDark
      ? 'rgba(30, 41, 59, 0.8)' // Dark slate background
      : 'rgba(248, 250, 252, 0.9)', // Light background
    backdropFilter: 'blur(12px)',
    border: `1px solid ${isDark ? 'rgba(71, 85, 105, 0.4)' : 'rgba(203, 213, 225, 0.6)'}`,
    boxShadow: isDark
      ? '0 8px 25px -5px rgba(0, 0, 0, 0.4), 0 4px 10px -3px rgba(0, 0, 0, 0.3)'
      : '0 4px 15px -3px rgba(0, 0, 0, 0.1), 0 2px 6px -1px rgba(0, 0, 0, 0.05)',
  };

  const hoverStyles = {
    transform: 'translateY(-4px) scale(1.02)',
    boxShadow: isDark
      ? '0 20px 40px -10px rgba(0, 0, 0, 0.5), 0 8px 20px -5px rgba(0, 0, 0, 0.4)'
      : '0 20px 40px -10px rgba(0, 0, 0, 0.15), 0 8px 20px -5px rgba(0, 0, 0, 0.1)',
    backgroundColor: isDark
      ? 'rgba(30, 41, 59, 0.95)'
      : 'rgba(248, 250, 252, 1)',
  };

  // Create gradient background for icon
  const getIconBackground = () => {
    if (!gradient) return color;

    // Create gradient based on the primary color
    const baseColor = color;
    const lighterColor = adjustColorBrightness(baseColor, 20);
    const darkerColor = adjustColorBrightness(baseColor, -20);

    return `linear-gradient(135deg, ${lighterColor} 0%, ${baseColor} 50%, ${darkerColor} 100%)`;
  };

  // Helper function to adjust color brightness
  const adjustColorBrightness = (hex: string, percent: number) => {
    const num = parseInt(hex.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = ((num >> 8) & 0x00ff) + amt;
    const B = (num & 0x0000ff) + amt;
    return (
      '#' +
      (
        0x1000000 +
        (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
        (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
        (B < 255 ? (B < 1 ? 0 : B) : 255)
      )
        .toString(16)
        .slice(1)
    );
  };

  return (
    <div
      className={baseClasses}
      style={tileStyles}
      onClick={handleClick}
      data-testid={testId}
      onMouseEnter={e => {
        if (!disabled) {
          Object.assign(e.currentTarget.style, hoverStyles);
        }
      }}
      onMouseLeave={e => {
        if (!disabled) {
          Object.assign(e.currentTarget.style, tileStyles);
        }
      }}
      tabIndex={disabled ? -1 : 0}
      role="button"
      aria-disabled={disabled}
    >
      {/* Subtle glow effect on hover */}
      <div
        className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        style={{
          background: `radial-gradient(circle at center, ${color}15, transparent 70%)`,
        }}
      />

      {/* Premium Badge */}
      {isPremium && (
        <div className="absolute top-2 right-2 z-20">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
            ⭐ PRO
          </div>
        </div>
      )}

      {/* Inactive Overlay */}
      {isInactive && (
        <div className="absolute inset-0 bg-black/20 rounded-xl flex items-center justify-center z-15">
          <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg">
            <div className="flex items-center gap-2 text-sm font-medium">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span style={{ color: colors.text }}>Inactive</span>
            </div>
          </div>
        </div>
      )}

      <div className="relative z-10 flex flex-col items-center text-center space-y-3 sm:space-y-4">
        {/* Icon with gradient background */}
        {icon && (
          <div className="relative">
            <div
              className={cn(
                "w-12 h-12 sm:w-14 sm:h-14 rounded-xl flex items-center justify-center text-white text-xl sm:text-2xl transition-all duration-300 shadow-lg",
                !isInactive && "group-hover:scale-110",
                isInactive && "grayscale"
              )}
              style={{
                background: getIconBackground(),
                boxShadow: `0 4px 15px -3px ${color}40, 0 2px 8px -1px ${color}20`,
              }}
            >
              {icon}
            </div>
            {/* Small highlight dot - only show for active apps */}
            {isActive && (
              <div
                className="absolute -top-1 -right-1 w-3 h-3 rounded-full opacity-80"
                style={{
                  background:
                    'linear-gradient(135deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4))',
                }}
              />
            )}
          </div>
        )}

        <div className="space-y-1">
          <h3
            className={cn(
              "font-semibold text-sm sm:text-base leading-tight",
              isInactive && "opacity-70"
            )}
            style={{ color: colors.text }}
          >
            {title}
          </h3>

          {description && (
            <p
              className={cn(
                "text-xs sm:text-sm opacity-70 leading-tight line-clamp-2",
                isInactive && "opacity-50"
              )}
              style={{ color: colors.textSecondary }}
            >
              {description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default AppTile;
