// Bot service for managing bots and automation
import type { 
  Bo<PERSON>, 
  BotCommand, 
  BotTrigger, 
  AutomationRule,
  ScheduledMessage,
  ApiResponse, 
  PaginatedResponse,
  Message 
} from '../types';

const API_BASE = '/api/discuss/bots';

export interface CreateBotRequest {
  name: string;
  description?: string;
  type: Bo<PERSON>['type'];
  commands: BotCommand[];
  triggers: BotTrigger[];
  config: Bot['config'];
}

export interface UpdateBotRequest {
  name?: string;
  description?: string;
  commands?: BotCommand[];
  triggers?: BotTrigger[];
  config?: Partial<Bot['config']>;
  enabled?: boolean;
}

export interface BotInteractionRequest {
  botId: string;
  command: string;
  parameters: Record<string, any>;
  channelId: string;
  userId: string;
  messageId?: string;
}

export interface BotInteractionResponse {
  success: boolean;
  response?: string;
  actions?: Array<{
    type: string;
    config: Record<string, any>;
  }>;
  error?: string;
}

export const botService = {
  // Get all bots
  async getBots(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<Bot>> {
    const response = await fetch(`${API_BASE}?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch bots');
    }
    
    return response.json();
  },

  // Get bot by ID
  async getBotById(botId: string): Promise<ApiResponse<Bot>> {
    const response = await fetch(`${API_BASE}/${botId}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch bot');
    }
    
    return response.json();
  },

  // Create new bot
  async createBot(request: CreateBotRequest): Promise<ApiResponse<Bot>> {
    const response = await fetch(API_BASE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create bot');
    }
    
    return response.json();
  },

  // Update bot
  async updateBot(botId: string, request: UpdateBotRequest): Promise<ApiResponse<Bot>> {
    const response = await fetch(`${API_BASE}/${botId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update bot');
    }
    
    return response.json();
  },

  // Delete bot
  async deleteBot(botId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${API_BASE}/${botId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete bot');
    }
    
    return response.json();
  },

  // Enable/disable bot
  async toggleBot(botId: string, enabled: boolean): Promise<ApiResponse<Bot>> {
    const response = await fetch(`${API_BASE}/${botId}/toggle`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ enabled }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to toggle bot');
    }
    
    return response.json();
  },

  // Interact with bot (send command)
  async interactWithBot(request: BotInteractionRequest): Promise<BotInteractionResponse> {
    const response = await fetch(`${API_BASE}/${request.botId}/interact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });
    
    if (!response.ok) {
      throw new Error('Failed to interact with bot');
    }
    
    return response.json();
  },

  // Get bot analytics
  async getBotAnalytics(botId: string, period: 'day' | 'week' | 'month' = 'week'): Promise<ApiResponse<any>> {
    const response = await fetch(`${API_BASE}/${botId}/analytics?period=${period}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch bot analytics');
    }
    
    return response.json();
  },

  // Test bot trigger
  async testBotTrigger(botId: string, triggerId: string, testData: any): Promise<BotInteractionResponse> {
    const response = await fetch(`${API_BASE}/${botId}/triggers/${triggerId}/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to test bot trigger');
    }
    
    return response.json();
  },

  // Process message for bot triggers
  async processMessage(message: Message): Promise<void> {
    // This would typically be called by the message service
    // when a new message is received to check for bot triggers
    const response = await fetch(`${API_BASE}/process-message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message }),
    });
    
    if (!response.ok) {
      console.error('Failed to process message for bots');
    }
  },
};

// Automation service
export const automationService = {
  // Get automation rules
  async getAutomationRules(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<AutomationRule>> {
    const response = await fetch(`/api/discuss/automation?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch automation rules');
    }
    
    return response.json();
  },

  // Create automation rule
  async createAutomationRule(rule: Omit<AutomationRule, 'id' | 'createdAt' | 'lastExecuted' | 'executionCount'>): Promise<ApiResponse<AutomationRule>> {
    const response = await fetch('/api/discuss/automation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(rule),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create automation rule');
    }
    
    return response.json();
  },

  // Update automation rule
  async updateAutomationRule(ruleId: string, updates: Partial<AutomationRule>): Promise<ApiResponse<AutomationRule>> {
    const response = await fetch(`/api/discuss/automation/${ruleId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update automation rule');
    }
    
    return response.json();
  },

  // Delete automation rule
  async deleteAutomationRule(ruleId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/discuss/automation/${ruleId}`, {
      method: 'DELETE',
    });
    
    if (!response.ok) {
      throw new Error('Failed to delete automation rule');
    }
    
    return response.json();
  },
};

// Scheduled message service
export const scheduledMessageService = {
  // Get scheduled messages
  async getScheduledMessages(page: number = 1, pageSize: number = 20): Promise<PaginatedResponse<ScheduledMessage>> {
    const response = await fetch(`/api/discuss/scheduled-messages?page=${page}&pageSize=${pageSize}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch scheduled messages');
    }
    
    return response.json();
  },

  // Create scheduled message
  async createScheduledMessage(message: Omit<ScheduledMessage, 'id' | 'status' | 'createdAt'>): Promise<ApiResponse<ScheduledMessage>> {
    const response = await fetch('/api/discuss/scheduled-messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create scheduled message');
    }
    
    return response.json();
  },

  // Cancel scheduled message
  async cancelScheduledMessage(messageId: string): Promise<ApiResponse<void>> {
    const response = await fetch(`/api/discuss/scheduled-messages/${messageId}/cancel`, {
      method: 'POST',
    });
    
    if (!response.ok) {
      throw new Error('Failed to cancel scheduled message');
    }
    
    return response.json();
  },
};
