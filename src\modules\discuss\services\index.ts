// Discuss module services
// These handle API calls and business logic for the discuss module

// Message services
export { messageService } from './messageService';
export type { SendMessageRequest, UpdateMessageRequest } from './messageService';

export { channelService } from './channelService';
export type { CreateChannelRequest, UpdateChannelRequest } from './channelService';

// Real-time services
export { websocketService } from './websocketService';
export type { WebSocketEventType, WebSocketEventHandler } from './websocketService';

// File services
export { fileService } from './fileService';
export type { UploadFileRequest, UploadProgress, FileUploadResponse } from './fileService';

// Bot and automation services
export { botService, automationService, scheduledMessageService } from './botService';
export type {
  CreateBotRequest,
  UpdateBotRequest,
  BotInteractionRequest,
  BotInteractionResponse
} from './botService';

export { botFramework } from './botFramework';
export type { BotContext, BotResponse, BotHandler } from './botFramework';

// Archival and compliance services
export {
  archivalService,
  retentionService,
  legalHoldService,
  exportService,
  complianceService
} from './archivalService';
export type {
  ArchiveSearchQuery,
  CreateRetentionPolicyRequest,
  CreateLegalHoldRequest,
  CreateExportRequest
} from './archivalService';

// Integration services
export {
  integrationService,
  erpIntegrationService,
  webhookService,
  apiKeyService
} from './integrationService';
export type {
  CreateIntegrationRequest,
  UpdateIntegrationRequest,
  CreateWebhookRequest,
  CreateAPIKeyRequest
} from './integrationService';

// Search services
export { searchService } from './searchService';
export type { SearchFilters, SearchResult, SearchResponse } from './searchService';

// Presence services
export { presenceService } from './presenceService';
export type { UpdatePresenceRequest, TypingRequest } from './presenceService';

// User services
// export { userService } from './userService';

// Notification services
export { notificationService } from './notificationService';
export type { NotificationPayload, NotificationAction, NotificationPreferences } from './notificationService';

// File services
// export { fileUploadService } from './fileUploadService';
// export { fileDownloadService } from './fileDownloadService';

// TODO: Implement remaining services
