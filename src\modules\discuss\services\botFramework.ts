// Bot framework for managing bot lifecycle and message processing
import type { 
  <PERSON><PERSON>, 
  BotTrigger, 
  BotAction, 
  Message, 
  User,
  Channel,
  TriggerCondition 
} from '../types';
import { websocketService } from './websocketService';
import { messageService } from './messageService';

export interface BotContext {
  message: Message;
  author: User;
  channel: Channel;
  mentions: User[];
  previousMessages: Message[];
  userContext: Record<string, any>;
}

export interface BotResponse {
  type: 'message' | 'dm' | 'reaction' | 'action';
  content?: string;
  channelId?: string;
  userId?: string;
  emoji?: string;
  actions?: BotAction[];
}

export class BotFramework {
  private registeredBots: Map<string, Bot> = new Map();
  private botHandlers: Map<string, BotHandler> = new Map();
  private contextStore: Map<string, Record<string, any>> = new Map();
  private rateLimitStore: Map<string, RateLimitData> = new Map();

  constructor() {
    this.initializeFramework();
  }

  private initializeFramework(): void {
    // Listen for new messages to process bot triggers
    websocketService.on('message_created', this.processMessage.bind(this));
    
    // Register built-in bot handlers
    this.registerBuiltInHandlers();
  }

  // Register a bot with the framework
  registerBot(bot: Bot, handler: BotHandler): void {
    this.registeredBots.set(bot.id, bot);
    this.botHandlers.set(bot.id, handler);
    console.log(`Bot registered: ${bot.name} (${bot.id})`);
  }

  // Unregister a bot
  unregisterBot(botId: string): void {
    this.registeredBots.delete(botId);
    this.botHandlers.delete(botId);
    this.contextStore.delete(botId);
    console.log(`Bot unregistered: ${botId}`);
  }

  // Process incoming message for bot triggers
  async processMessage(event: any): Promise<void> {
    const message: Message = event.message;
    
    // Skip bot messages to prevent loops
    if (this.isBotMessage(message)) {
      return;
    }

    // Check all registered bots for triggers
    for (const [botId, bot] of this.registeredBots) {
      if (!bot.enabled) continue;

      try {
        await this.checkBotTriggers(bot, message);
      } catch (error) {
        console.error(`Error processing message for bot ${bot.name}:`, error);
      }
    }
  }

  // Check if a message triggers any bot actions
  private async checkBotTriggers(bot: Bot, message: Message): Promise<void> {
    const context = await this.buildBotContext(bot, message);

    for (const trigger of bot.triggers) {
      if (!trigger.enabled) continue;

      if (await this.evaluateTrigger(trigger, context)) {
        // Check rate limiting
        if (!this.checkRateLimit(bot.id, context.author.id)) {
          console.warn(`Rate limit exceeded for bot ${bot.name} and user ${context.author.id}`);
          continue;
        }

        // Execute bot action
        await this.executeBotAction(bot, trigger.action, context);
        
        // Update rate limiting
        this.updateRateLimit(bot.id, context.author.id);
      }
    }
  }

  // Evaluate if a trigger condition is met
  private async evaluateTrigger(trigger: BotTrigger, context: BotContext): Promise<boolean> {
    const { message } = context;

    switch (trigger.type) {
      case 'keyword':
        return this.evaluateKeywordTrigger(trigger.pattern, message.content);
      
      case 'mention':
        return message.mentions.length > 0;
      
      case 'regex':
        return this.evaluateRegexTrigger(trigger.pattern, message.content);
      
      case 'event':
        // Event triggers are handled separately
        return false;
      
      default:
        return false;
    }
  }

  private evaluateKeywordTrigger(pattern: string, content: string): boolean {
    const keywords = pattern.toLowerCase().split(',').map(k => k.trim());
    const messageContent = content.toLowerCase();
    return keywords.some(keyword => messageContent.includes(keyword));
  }

  private evaluateRegexTrigger(pattern: string, content: string): boolean {
    try {
      const regex = new RegExp(pattern, 'i');
      return regex.test(content);
    } catch (error) {
      console.error('Invalid regex pattern:', pattern);
      return false;
    }
  }

  // Execute bot action
  private async executeBotAction(bot: Bot, action: BotAction, context: BotContext): Promise<void> {
    const handler = this.botHandlers.get(bot.id);
    if (!handler) {
      console.error(`No handler found for bot ${bot.id}`);
      return;
    }

    try {
      const response = await handler.handleAction(action, context);
      if (response) {
        await this.sendBotResponse(bot, response, context);
      }
    } catch (error) {
      console.error(`Error executing bot action for ${bot.name}:`, error);
    }
  }

  // Send bot response
  private async sendBotResponse(bot: Bot, response: BotResponse, context: BotContext): Promise<void> {
    switch (response.type) {
      case 'message':
        if (response.content) {
          await messageService.sendMessage({
            content: response.content,
            channelId: response.channelId || context.message.channelId,
          });
        }
        break;
      
      case 'dm':
        if (response.content && response.userId) {
          // Send direct message (implementation depends on DM system)
          console.log(`Sending DM to ${response.userId}: ${response.content}`);
        }
        break;
      
      case 'reaction':
        if (response.emoji) {
          await messageService.addReaction(context.message.id, response.emoji);
        }
        break;
      
      case 'action':
        // Execute additional actions
        if (response.actions) {
          for (const action of response.actions) {
            await this.executeBotAction(bot, action, context);
          }
        }
        break;
    }
  }

  // Build context for bot processing
  private async buildBotContext(bot: Bot, message: Message): Promise<BotContext> {
    // This would fetch additional context data
    // For now, return minimal context
    return {
      message,
      author: { id: message.authorId, name: 'User', email: '', status: 'online' },
      channel: { id: message.channelId || '', name: 'Channel', type: 'public', memberIds: [], createdBy: '', createdAt: new Date(), isArchived: false, settings: { notifications: true, allowFileUploads: true, allowExternalLinks: true } },
      mentions: [],
      previousMessages: [],
      userContext: this.getUserContext(bot.id, message.authorId),
    };
  }

  // Get user context for bot
  private getUserContext(botId: string, userId: string): Record<string, any> {
    const botContext = this.contextStore.get(botId) || {};
    return botContext[userId] || {};
  }

  // Update user context for bot
  private updateUserContext(botId: string, userId: string, context: Record<string, any>): void {
    if (!this.contextStore.has(botId)) {
      this.contextStore.set(botId, {});
    }
    const botContext = this.contextStore.get(botId)!;
    botContext[userId] = { ...botContext[userId], ...context };
  }

  // Check rate limiting
  private checkRateLimit(botId: string, userId: string): boolean {
    const key = `${botId}:${userId}`;
    const data = this.rateLimitStore.get(key);
    
    if (!data) return true;
    
    const now = Date.now();
    const minuteAgo = now - 60 * 1000;
    const hourAgo = now - 60 * 60 * 1000;
    
    // Clean old entries
    data.requests = data.requests.filter(time => time > hourAgo);
    
    const recentRequests = data.requests.filter(time => time > minuteAgo);
    
    // Check limits (these would come from bot config)
    return recentRequests.length < 10; // Max 10 requests per minute
  }

  // Update rate limiting
  private updateRateLimit(botId: string, userId: string): void {
    const key = `${botId}:${userId}`;
    const data = this.rateLimitStore.get(key) || { requests: [] };
    
    data.requests.push(Date.now());
    this.rateLimitStore.set(key, data);
  }

  // Check if message is from a bot
  private isBotMessage(message: Message): boolean {
    // This would check if the author is a bot
    // For now, simple check based on author ID pattern
    return message.authorId.startsWith('bot-');
  }

  // Register built-in bot handlers
  private registerBuiltInHandlers(): void {
    // FAQ Bot Handler
    this.botHandlers.set('faq-bot', new FAQBotHandler());
    
    // AI Assistant Handler
    this.botHandlers.set('ai-assistant', new AIAssistantHandler());
    
    // Workflow Bot Handler
    this.botHandlers.set('workflow-bot', new WorkflowBotHandler());
  }
}

// Bot handler interface
export interface BotHandler {
  handleAction(action: BotAction, context: BotContext): Promise<BotResponse | null>;
  handleCommand?(command: string, parameters: Record<string, any>, context: BotContext): Promise<BotResponse | null>;
}

// Rate limiting data structure
interface RateLimitData {
  requests: number[];
}

// Built-in bot handlers
class FAQBotHandler implements BotHandler {
  async handleAction(action: BotAction, context: BotContext): Promise<BotResponse | null> {
    // FAQ bot logic
    return {
      type: 'message',
      content: 'This is a FAQ response based on your question.',
    };
  }
}

class AIAssistantHandler implements BotHandler {
  async handleAction(action: BotAction, context: BotContext): Promise<BotResponse | null> {
    // AI assistant logic
    return {
      type: 'message',
      content: 'AI assistant response would go here.',
    };
  }
}

class WorkflowBotHandler implements BotHandler {
  async handleAction(action: BotAction, context: BotContext): Promise<BotResponse | null> {
    // Workflow automation logic
    return {
      type: 'action',
      actions: [
        {
          type: 'workflow',
          config: action.config,
        },
      ],
    };
  }
}

// Export singleton instance
export const botFramework = new BotFramework();
